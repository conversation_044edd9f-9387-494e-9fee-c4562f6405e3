<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Arizona Address Parsing Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        button { padding: 10px 20px; margin: 5px; }
        .success { color: green; }
        .error { color: red; }
        table { border-collapse: collapse; width: 100%; margin-top: 10px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .good { background-color: #d4edda; }
        .bad { background-color: #f8d7da; }
    </style>
</head>
<body>
    <h1>Arizona Address Parsing Test</h1>
    
    <div class="test-section">
        <h2>Address Format Testing</h2>
        <button onclick="testAddressFormats()">Test Various Address Formats</button>
        <div id="testResult"></div>
    </div>

    <div class="test-section">
        <h2>Fix Current Data</h2>
        <button onclick="fixCurrentData()">Re-parse All Current Data</button>
        <div id="fixResult"></div>
    </div>

    <script>
        function parseAddress(address) {
            // Handle null, undefined, empty, or "\N" addresses
            if (!address || address === '\\N' || address.trim() === '') {
                return { city: null, state: null }
            }

            // Common address formats:
            // "1300 Burtonwood Cir, Charlotte, NC 28212"
            // "Charlotte, NC 28212"
            // "Charlotte, NC"
            // "Phoenix AZ 85001" (without commas)
            // "123 Main St Phoenix AZ 85001"
            
            // First try comma-separated format
            const parts = address.split(',').map(part => part.trim())
            
            if (parts.length >= 2) {
                // Get the last part which should contain state (and possibly zip)
                const lastPart = parts[parts.length - 1]
                // Get the second to last part which should be the city
                const cityPart = parts[parts.length - 2]
                
                // Extract state (first 2 letters of last part)
                const stateMatch = lastPart.match(/^([A-Z]{2})/i)
                const state = stateMatch ? stateMatch[1].toUpperCase() : null
                
                // City is the second to last part
                const city = cityPart || null
                
                return { city, state }
            }
            
            // If no commas, try space-separated format (common in some datasets)
            // Look for pattern: "City ST 12345" or "City ST"
            const spaceMatch = address.match(/\b([A-Za-z\s]+?)\s+([A-Z]{2})\s*(\d{5})?/i)
            if (spaceMatch) {
                const city = spaceMatch[1].trim()
                const state = spaceMatch[2].toUpperCase()
                return { city, state }
            }
            
            return { city: null, state: null }
        }

        function testAddressFormats() {
            const testAddresses = [
                // Standard comma-separated formats
                "123 Main St, Phoenix, AZ 85001",
                "456 Oak Ave, Tucson, AZ 85701",
                "Phoenix, AZ 85001",
                "Scottsdale, AZ",
                
                // Space-separated formats (no commas)
                "123 Main St Phoenix AZ 85001",
                "456 Oak Ave Tucson AZ 85701",
                "Phoenix AZ 85001",
                "Scottsdale AZ",
                
                // Edge cases
                "Mesa AZ",
                "Chandler Arizona 85224",
                "Glendale, Arizona 85301",
                "\\N",
                "",
                null,
                
                // Multi-word cities
                "123 Main St, Casa Grande, AZ 85122",
                "456 Oak Ave Casa Grande AZ 85122",
                "Sun City West, AZ 85375",
                "Sun City West AZ 85375"
            ];

            let result = '<div class="success">✓ Address Parsing Test Results:</div>';
            result += '<table><tr><th>Address</th><th>Parsed City</th><th>Parsed State</th><th>Status</th></tr>';

            testAddresses.forEach(address => {
                const parsed = parseAddress(address);
                const hasCity = parsed.city !== null;
                const hasState = parsed.state !== null;
                const status = hasCity && hasState ? 'GOOD' : 'NEEDS REVIEW';
                const rowClass = status === 'GOOD' ? 'good' : 'bad';

                result += `<tr class="${rowClass}">`;
                result += `<td>${address || 'NULL'}</td>`;
                result += `<td>${parsed.city || 'NULL'}</td>`;
                result += `<td>${parsed.state || 'NULL'}</td>`;
                result += `<td>${status}</td>`;
                result += `</tr>`;
            });

            result += '</table>';
            document.getElementById('testResult').innerHTML = result;
        }

        function fixCurrentData() {
            const companies = JSON.parse(localStorage.getItem('companies')) || [];
            
            if (companies.length === 0) {
                document.getElementById('fixResult').innerHTML = '<span class="error">No data found in localStorage</span>';
                return;
            }

            let updated = 0;
            let beforeStates = new Set();
            let afterStates = new Set();

            companies.forEach(company => {
                if (company.state) beforeStates.add(company.state);
                
                if (company.originalData?.address) {
                    const { city, state } = parseAddress(company.originalData.address);
                    if (city !== company.city || state !== company.state) {
                        company.city = city;
                        company.state = state;
                        updated++;
                    }
                }
                
                if (company.state) afterStates.add(company.state);
            });

            // Save back to localStorage
            localStorage.setItem('companies', JSON.stringify(companies));

            let result = `<div class="success">✓ Re-parsing Complete</div>`;
            result += `<div><strong>Companies Updated:</strong> ${updated}</div>`;
            result += `<div><strong>States Before:</strong> ${Array.from(beforeStates).sort().join(', ')}</div>`;
            result += `<div><strong>States After:</strong> ${Array.from(afterStates).sort().join(', ')}</div>`;
            result += `<div>Data has been saved to localStorage. <strong>Refresh the main app</strong> to see changes in dropdowns.</div>`;

            document.getElementById('fixResult').innerHTML = result;
        }

        // Auto-run test on page load
        window.onload = function() {
            testAddressFormats();
        }
    </script>
</body>
</html>
