<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cold Calling App - Standalone Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        button { padding: 10px 20px; margin: 5px; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>Cold Calling App - Connection Test</h1>
    
    <div class="test-section">
        <h2>Basic JavaScript Test</h2>
        <button onclick="testBasicJS()">Test Basic JavaScript</button>
        <div id="jsResult"></div>
    </div>

    <div class="test-section">
        <h2>LocalStorage Test</h2>
        <button onclick="testLocalStorage()">Test LocalStorage</button>
        <div id="storageResult"></div>
    </div>

    <div class="test-section">
        <h2>Address Parsing Test</h2>
        <button onclick="testAddressParsing()">Test Address Parsing</button>
        <div id="addressResult"></div>
    </div>

    <script>
        function testBasicJS() {
            try {
                document.getElementById('jsResult').innerHTML = '<span class="success">✓ Basic JavaScript working</span>';
            } catch (error) {
                document.getElementById('jsResult').innerHTML = '<span class="error">✗ JavaScript error: ' + error.message + '</span>';
            }
        }

        function testLocalStorage() {
            try {
                localStorage.setItem('test', 'working');
                const result = localStorage.getItem('test');
                localStorage.removeItem('test');
                
                if (result === 'working') {
                    document.getElementById('storageResult').innerHTML = '<span class="success">✓ LocalStorage working</span>';
                } else {
                    document.getElementById('storageResult').innerHTML = '<span class="error">✗ LocalStorage not working properly</span>';
                }
            } catch (error) {
                document.getElementById('storageResult').innerHTML = '<span class="error">✗ LocalStorage error: ' + error.message + '</span>';
            }
        }

        function parseAddress(address) {
            if (!address || address === '\\N' || address.trim() === '') {
                return { city: null, state: null }
            }

            const parts = address.split(',').map(part => part.trim())
            
            if (parts.length >= 2) {
                const lastPart = parts[parts.length - 1]
                const cityPart = parts[parts.length - 2]
                
                const stateMatch = lastPart.match(/^([A-Z]{2})/i)
                const state = stateMatch ? stateMatch[1].toUpperCase() : null
                const city = cityPart || null
                
                return { city, state }
            }
            
            return { city: null, state: null }
        }

        function testAddressParsing() {
            try {
                const testAddresses = [
                    "123 Main St, Charlotte, NC 28202",
                    "456 Oak Ave, Raleigh, NC 27601", 
                    "789 Beach Blvd, Miami, FL 33101"
                ];

                let results = '<div class="success">✓ Address parsing working:</div>';
                testAddresses.forEach(addr => {
                    const parsed = parseAddress(addr);
                    results += `<div>Address: ${addr} → City: ${parsed.city}, State: ${parsed.state}</div>`;
                });

                document.getElementById('addressResult').innerHTML = results;
            } catch (error) {
                document.getElementById('addressResult').innerHTML = '<span class="error">✗ Address parsing error: ' + error.message + '</span>';
            }
        }

        // Auto-run tests on page load
        window.onload = function() {
            console.log('Page loaded, running tests...');
            testBasicJS();
            testLocalStorage();
            testAddressParsing();
        }
    </script>
</body>
</html>
