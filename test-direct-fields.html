<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Direct City/State Fields Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        button { padding: 10px 20px; margin: 5px; }
        .success { color: green; }
        .error { color: red; }
        table { border-collapse: collapse; width: 100%; margin-top: 10px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .good { background-color: #d4edda; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Direct City/State Fields Test</h1>
    
    <div class="test-section">
        <h2>Test Data Processing</h2>
        <button onclick="testDataProcessing()">Test with Sample JSON Data</button>
        <div id="testResult"></div>
    </div>

    <div class="test-section">
        <h2>Fix Current Data</h2>
        <button onclick="fixCurrentData()">Re-process Current Data with Direct Fields</button>
        <div id="fixResult"></div>
    </div>

    <script>
        function normalizeStateName(state) {
            if (!state) return null
            
            // If already 2 letters, return as-is (uppercase)
            if (state.length === 2) {
                return state.toUpperCase()
            }
            
            // Map of full state names to abbreviations
            const stateMap = {
                'alabama': 'AL', 'alaska': 'AK', 'arizona': 'AZ', 'arkansas': 'AR', 'california': 'CA',
                'colorado': 'CO', 'connecticut': 'CT', 'delaware': 'DE', 'florida': 'FL', 'georgia': 'GA',
                'hawaii': 'HI', 'idaho': 'ID', 'illinois': 'IL', 'indiana': 'IN', 'iowa': 'IA',
                'kansas': 'KS', 'kentucky': 'KY', 'louisiana': 'LA', 'maine': 'ME', 'maryland': 'MD',
                'massachusetts': 'MA', 'michigan': 'MI', 'minnesota': 'MN', 'mississippi': 'MS', 'missouri': 'MO',
                'montana': 'MT', 'nebraska': 'NE', 'nevada': 'NV', 'new hampshire': 'NH', 'new jersey': 'NJ',
                'new mexico': 'NM', 'new york': 'NY', 'north carolina': 'NC', 'north dakota': 'ND', 'ohio': 'OH',
                'oklahoma': 'OK', 'oregon': 'OR', 'pennsylvania': 'PA', 'rhode island': 'RI', 'south carolina': 'SC',
                'south dakota': 'SD', 'tennessee': 'TN', 'texas': 'TX', 'utah': 'UT', 'vermont': 'VT',
                'virginia': 'VA', 'washington': 'WA', 'west virginia': 'WV', 'wisconsin': 'WI', 'wyoming': 'WY'
            }
            
            const normalized = stateMap[state.toLowerCase()]
            return normalized || state.toUpperCase()
        }

        function processItem(item) {
            // Use existing city/state fields if available, otherwise parse from address
            let city = item.city || null
            let state = item.state || null
            
            // Handle full state names (convert "North Carolina" to "NC")
            if (state) {
                state = normalizeStateName(state)
            }
            
            return {
                title: item.title,
                city: city,
                state: state,
                originalCity: item.city,
                originalState: item.state,
                address: item.address
            }
        }

        function testDataProcessing() {
            // Sample data matching your JSON format
            const sampleData = [
                {
                    "title": "Revival Exterior Cleaning & Pressure Washing",
                    "categoryName": "Pressure washing service",
                    "address": "4320 Craig Ave, Charlotte, NC 28211",
                    "city": "Charlotte",
                    "state": "North Carolina",
                    "phone": "(*************",
                    "website": "https://revivalexteriornc.com"
                },
                {
                    "title": "Arizona Power Wash",
                    "categoryName": "Pressure washing service", 
                    "address": "123 Main St, Phoenix, AZ 85001",
                    "city": "Phoenix",
                    "state": "Arizona",
                    "phone": "(*************",
                    "website": "https://azpowerwash.com"
                },
                {
                    "title": "Scottsdale Clean",
                    "categoryName": "Pressure washing service",
                    "address": "456 Oak Ave, Scottsdale, AZ 85251", 
                    "city": "Scottsdale",
                    "state": "AZ",
                    "phone": "(*************",
                    "website": "https://scottsdaleClean.com"
                }
            ];

            let result = '<div class="success">✓ Data Processing Test Results:</div>';
            result += '<table><tr><th>Company</th><th>Original City</th><th>Original State</th><th>Processed City</th><th>Processed State</th></tr>';

            sampleData.forEach(item => {
                const processed = processItem(item);
                result += `<tr class="good">`;
                result += `<td>${processed.title}</td>`;
                result += `<td>${processed.originalCity || 'NULL'}</td>`;
                result += `<td>${processed.originalState || 'NULL'}</td>`;
                result += `<td>${processed.city || 'NULL'}</td>`;
                result += `<td>${processed.state || 'NULL'}</td>`;
                result += `</tr>`;
            });

            result += '</table>';
            result += '<div class="success">✓ All states should be normalized to 2-letter codes (NC, AZ, etc.)</div>';
            
            document.getElementById('testResult').innerHTML = result;
        }

        function fixCurrentData() {
            const companies = JSON.parse(localStorage.getItem('companies')) || [];
            
            if (companies.length === 0) {
                document.getElementById('fixResult').innerHTML = '<span class="error">No data found in localStorage</span>';
                return;
            }

            let updated = 0;
            let beforeStates = new Set();
            let afterStates = new Set();
            let beforeCities = new Set();
            let afterCities = new Set();

            companies.forEach(company => {
                if (company.state) beforeStates.add(company.state);
                if (company.city) beforeCities.add(company.city);
                
                // Use direct city/state fields if available
                let city = company.originalData?.city || company.city || null;
                let state = company.originalData?.state || company.state || null;
                
                // Normalize state name
                if (state) {
                    state = normalizeStateName(state);
                }
                
                if (city !== company.city || state !== company.state) {
                    company.city = city;
                    company.state = state;
                    updated++;
                }
                
                if (company.state) afterStates.add(company.state);
                if (company.city) afterCities.add(company.city);
            });

            // Save back to localStorage
            localStorage.setItem('companies', JSON.stringify(companies));

            let result = `<div class="success">✓ Re-processing Complete</div>`;
            result += `<div><strong>Companies Updated:</strong> ${updated}</div>`;
            result += `<div><strong>States Before:</strong> ${Array.from(beforeStates).sort().join(', ')}</div>`;
            result += `<div><strong>States After:</strong> ${Array.from(afterStates).sort().join(', ')}</div>`;
            result += `<div><strong>Cities Found:</strong> ${afterCities.size} unique cities</div>`;
            result += `<div>Data has been saved to localStorage. <strong>Refresh the main app</strong> to see changes in dropdowns.</div>`;

            document.getElementById('fixResult').innerHTML = result;
        }

        // Auto-run test on page load
        window.onload = function() {
            testDataProcessing();
        }
    </script>
</body>
</html>
