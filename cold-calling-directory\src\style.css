﻿:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color: #213547;
  background-color: #ffffff;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  background-color: #f5f5f5;
}

#app {
  max-width: 1400px;
  margin: 0 auto;
  padding: 1rem;
}

/* App Header */
.app-header {
  text-align: center;
  margin-bottom: 2rem;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.app-header h1 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
}

.app-header p {
  margin: 0;
  color: #7f8c8d;
}

/* Upload Section */
.upload-section {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 2rem;
}

.upload-section h2 {
  margin-top: 0;
  color: #2c3e50;
}

.upload-section input[type="file"] {
  margin-right: 1rem;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
}

/* Filter Section */
.filter-section {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 2rem;
}

.filter-section h3 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
}

.filter-controls {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.filter-group {
  display: flex;
  flex-direction: column;
}

.filter-group label {
  font-weight: 600;
  color: #34495e;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.filter-section select {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  background: white;
  cursor: pointer;
}

.filter-section select:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* Companies Section */
.companies-section {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  width: 100%;
}

.companies-section h3 {
  margin-top: 0;
  color: #2c3e50;
}

.companies-list {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(auto-fit, minmax(600px, 1fr));
}

.no-companies {
  text-align: center;
  color: #7f8c8d;
  font-style: italic;
  padding: 2rem;
}

/* Company Cards */
.company-card {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 1.5rem;
  background: #fafafa;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
}

.company-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  border-color: #3498db;
}

/* Color-coded cards based on disposition */
.company-card.card-not-available {
  background: #fff3cd;
  border-color: #ffc107;
  border-left: 4px solid #ffc107;
}

.company-card.card-not-interested {
  background: #f8d7da;
  border-color: #dc3545;
  border-left: 4px solid #dc3545;
}

.company-card.card-call-back {
  background: #d1ecf1;
  border-color: #17a2b8;
  border-left: 4px solid #17a2b8;
}

.company-card.card-sold {
  background: #d4edda;
  border-color: #28a745;
  border-left: 4px solid #28a745;
}

.company-info h4 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.2rem;
}

.contact-details p {
  margin: 0.5rem 0;
  font-size: 0.9rem;
}

.contact-details strong {
  color: #34495e;
}

.contact-details a {
  color: #3498db;
  text-decoration: none;
  word-break: break-all;
  overflow-wrap: break-word;
}

.contact-details a:hover {
  text-decoration: underline;
}

.contact-details p {
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Card Action */
.card-action {
  margin-top: 1rem;
  text-align: center;
}

.view-in-dialer {
  display: inline-block;
  background: #3498db;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  transition: background-color 0.3s ease;
}

.company-card:hover .view-in-dialer {
  background: #2980b9;
}

/* Call Management */
.call-management {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.disposition-section,
.notes-section {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.disposition-section label,
.notes-section label {
  font-weight: 600;
  color: #2c3e50;
}

.disposition-select {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
}

.call-notes {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
}

/* Buttons */
button {
  border-radius: 4px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 0.9em;
  font-weight: 500;
  font-family: inherit;
  background-color: #3498db;
  color: white;
  cursor: pointer;
  transition: background-color 0.25s;
}

button:hover {
  background-color: #2980b9;
}

button:focus,
button:focus-visible {
  outline: 2px solid #3498db;
  outline-offset: 2px;
}

.save-notes-btn {
  background-color: #27ae60;
  align-self: flex-start;
}

.save-notes-btn:hover {
  background-color: #229954;
}

/* View Controls */
.view-controls {
  background: white;
  padding: 1rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 2rem;
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.view-btn {
  padding: 0.8rem 1.5rem;
  border: 2px solid #3498db;
  background: white;
  color: #3498db;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.25s;
}

.view-btn:hover {
  background: #3498db;
  color: white;
}

.view-btn.active {
  background: #3498db;
  color: white;
}

/* Power Dialer Styles */
.power-dialer {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 0.5rem;
  box-sizing: border-box;
}

.power-dialer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: white;
  padding: 0.8rem 1.2rem;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 0.5rem;
  flex-shrink: 0;
}

.power-dialer-header h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.3rem;
}

.back-btn {
  background: #95a5a6;
  color: white;
  border: none;
  padding: 0.4rem 0.8rem;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  font-size: 0.9rem;
}

.back-btn:hover {
  background: #7f8c8d;
}

.progress-info {
  color: #7f8c8d;
  font-weight: 500;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.restored-indicator {
  background: #27ae60;
  color: white;
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

.dialer-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: white;
  padding: 0.8rem;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 0.5rem;
  flex-shrink: 0;
}

.nav-btn {
  padding: 0.6rem 1.2rem;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.25s;
  font-size: 0.9rem;
}

.nav-btn:hover:not(:disabled) {
  background: #2980b9;
}

.nav-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
}

.lead-counter {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
}

.current-lead {
  background: white;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  overflow: hidden;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  width: 100%;
}

.lead-info {
  padding: 1rem;
  border-bottom: 1px solid #ecf0f1;
  flex-shrink: 0;
}

.lead-info h3 {
  margin: 0 0 0.8rem 0;
  color: #2c3e50;
  font-size: 1.3rem;
}

.contact-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.2rem;
  margin-bottom: 0.8rem;
}

.contact-item {
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
}

.contact-item.address-item {
  grid-column: span 4;
}

.contact-item label {
  font-weight: 600;
  color: #34495e;
  font-size: 0.8rem;
}

.contact-item div {
  font-size: 0.9rem;
  line-height: 1.3;
}

.address {
  color: #7f8c8d;
  font-style: italic;
}

/* Editable Fields */
.editable-field {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-height: 1.5rem;
}

.field-display {
  flex: 1;
  cursor: pointer;
  padding: 0.4rem 0.6rem;
  border-radius: 3px;
  transition: background-color 0.2s;
  font-size: 0.9rem;
  min-height: 1.4rem;
  display: flex;
  align-items: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.field-display:hover {
  background-color: #f8f9fa;
  border: 1px dashed #3498db;
}

.field-display:empty::before {
  content: attr(data-placeholder);
  color: #999;
  font-style: italic;
}

.field-input {
  flex: 1;
  padding: 0.4rem 0.6rem;
  border: 1px solid #3498db;
  border-radius: 3px;
  font-size: 0.9rem;
  background: white;
}

.field-input:focus {
  outline: none;
  border-color: #2980b9;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.field-input.hidden {
  display: none;
}

.field-display.hidden {
  display: none;
}

.call-link, .visit-link, .email-link {
  font-size: 0.75rem;
  padding: 0.2rem 0.4rem;
  background: #3498db;
  color: white;
  text-decoration: none;
  border-radius: 3px;
  white-space: nowrap;
  flex-shrink: 0;
}

.call-link:hover, .visit-link:hover, .email-link:hover {
  background: #2980b9;
}

.phone-number a {
  color: #27ae60;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
}

.phone-number a:hover {
  text-decoration: underline;
}

.status-indicator {
  padding: 0.4rem 0.8rem;
  border-radius: 15px;
  font-weight: 600;
  text-align: center;
  margin-top: 0.5rem;
  font-size: 0.8rem;
}

.status-indicator.not-called {
  background: #ecf0f1;
  color: #7f8c8d;
}

.status-indicator.not-available {
  background: #f39c12;
  color: white;
}

.status-indicator.not-interested {
  background: #e74c3c;
  color: white;
}

.status-indicator.call-back {
  background: #3498db;
  color: white;
}

.status-indicator.sold {
  background: #27ae60;
  color: white;
}

.call-actions {
  padding: 1rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.disposition-section {
  margin-bottom: 1rem;
  flex-shrink: 0;
}

.disposition-section label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.9rem;
}

.disposition-buttons {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.4rem;
}

.disposition-btn {
  padding: 0.5rem 0.3rem;
  border: 2px solid #bdc3c7;
  background: white;
  color: #7f8c8d;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.25s;
  font-size: 0.8rem;
}

.disposition-btn:hover {
  border-color: #95a5a6;
  color: #2c3e50;
}

/* Color-coded disposition buttons */
.disposition-not-available {
  border-color: #ffc107;
  color: #856404;
}

.disposition-not-available:hover {
  background: #fff3cd;
}

.disposition-not-available.active {
  background: #ffc107;
  border-color: #ffc107;
  color: white;
}

.disposition-not-interested {
  border-color: #dc3545;
  color: #721c24;
}

.disposition-not-interested:hover {
  background: #f8d7da;
}

.disposition-not-interested.active {
  background: #dc3545;
  border-color: #dc3545;
  color: white;
}

.disposition-call-back {
  border-color: #17a2b8;
  color: #0c5460;
}

.disposition-call-back:hover {
  background: #d1ecf1;
}

.disposition-call-back.active {
  background: #17a2b8;
  border-color: #17a2b8;
  color: white;
}

.disposition-sold {
  border-color: #28a745;
  color: #155724;
}

.disposition-sold:hover {
  background: #d4edda;
}

.disposition-sold.active {
  background: #28a745;
  border-color: #28a745;
  color: white;
}

.notes-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem;
  min-height: 0;
}

.notes-section label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.9rem;
  flex-shrink: 0;
}

.call-notes {
  flex: 1;
  min-height: 60px;
  resize: none;
  font-size: 0.9rem;
}

.quick-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  flex-shrink: 0;
}

.next-btn {
  padding: 0.8rem 2rem;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.25s;
}

.next-btn:hover {
  background: #2980b9;
}

.keyboard-shortcuts {
  text-align: center;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 4px;
  color: #6c757d;
  font-size: 0.75rem;
  flex-shrink: 0;
}

.no-leads {
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Responsive Design */
@media (max-width: 768px) {
  .company-card {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .contact-grid {
    grid-template-columns: 1fr 1fr;
  }

  .contact-item.address-item {
    grid-column: span 2;
  }

  .disposition-buttons {
    grid-template-columns: repeat(2, 1fr);
  }

  .quick-actions {
    flex-direction: column;
  }

  .view-controls {
    flex-direction: column;
  }

  .power-dialer {
    padding: 0.25rem;
  }

  #app {
    padding: 0.25rem;
  }
}
