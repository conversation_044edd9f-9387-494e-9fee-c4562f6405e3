<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Arizona Data Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        button { padding: 10px 20px; margin: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; max-height: 300px; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .highlight { background-color: yellow; }
    </style>
</head>
<body>
    <h1>Arizona Data Debug Tool</h1>
    
    <div class="section">
        <h2>Current Data Analysis</h2>
        <button onclick="analyzeCurrentData()">Analyze Current Data</button>
        <div id="analysisResult"></div>
    </div>

    <div class="section">
        <h2>Address Parsing Test</h2>
        <button onclick="testAddressParsing()">Test Address Parsing</button>
        <div id="parsingResult"></div>
    </div>

    <div class="section">
        <h2>Raw Data Inspection</h2>
        <button onclick="showRawData()">Show Raw Company Data</button>
        <div id="rawDataResult"></div>
    </div>

    <div class="section">
        <h2>Fix Missing City/State</h2>
        <button onclick="fixMissingCityState()">Re-parse All Addresses</button>
        <div id="fixResult"></div>
    </div>

    <script>
        function parseAddress(address) {
            // Handle null, undefined, empty, or "\N" addresses
            if (!address || address === '\\N' || address.trim() === '') {
                return { city: null, state: null }
            }

            // Common address formats:
            // "1300 Burtonwood Cir, Charlotte, NC 28212"
            // "Charlotte, NC 28212"
            // "Charlotte, NC"
            
            // Split by commas and get the last parts
            const parts = address.split(',').map(part => part.trim())
            
            if (parts.length >= 2) {
                // Get the last part which should contain state (and possibly zip)
                const lastPart = parts[parts.length - 1]
                // Get the second to last part which should be the city
                const cityPart = parts[parts.length - 2]
                
                // Extract state (first 2 letters of last part)
                const stateMatch = lastPart.match(/^([A-Z]{2})/i)
                const state = stateMatch ? stateMatch[1].toUpperCase() : null
                
                // City is the second to last part
                const city = cityPart || null
                
                return { city, state }
            }
            
            return { city: null, state: null }
        }

        function analyzeCurrentData() {
            const companies = JSON.parse(localStorage.getItem('companies')) || [];
            
            if (companies.length === 0) {
                document.getElementById('analysisResult').innerHTML = '<span class="error">No data found in localStorage</span>';
                return;
            }

            // Count by state
            const stateCounts = {};
            const cityCounts = {};
            let missingCity = 0;
            let missingState = 0;
            let missingAddress = 0;

            companies.forEach(company => {
                if (!company.city) missingCity++;
                if (!company.state) missingState++;
                if (!company.originalData?.address) missingAddress++;

                if (company.state) {
                    stateCounts[company.state] = (stateCounts[company.state] || 0) + 1;
                }
                if (company.city) {
                    cityCounts[company.city] = (cityCounts[company.city] || 0) + 1;
                }
            });

            let result = `<div class="success">✓ Data Analysis Complete</div>`;
            result += `<div><strong>Total Companies:</strong> ${companies.length}</div>`;
            result += `<div><strong>Missing City:</strong> ${missingCity}</div>`;
            result += `<div><strong>Missing State:</strong> ${missingState}</div>`;
            result += `<div><strong>Missing Address:</strong> ${missingAddress}</div>`;
            
            result += `<h3>States Found:</h3>`;
            result += `<table><tr><th>State</th><th>Count</th></tr>`;
            Object.entries(stateCounts).sort().forEach(([state, count]) => {
                result += `<tr><td>${state}</td><td>${count}</td></tr>`;
            });
            result += `</table>`;

            result += `<h3>Top Cities:</h3>`;
            result += `<table><tr><th>City</th><th>Count</th></tr>`;
            Object.entries(cityCounts)
                .sort((a, b) => b[1] - a[1])
                .slice(0, 10)
                .forEach(([city, count]) => {
                    result += `<tr><td>${city}</td><td>${count}</td></tr>`;
                });
            result += `</table>`;

            document.getElementById('analysisResult').innerHTML = result;
        }

        function testAddressParsing() {
            const companies = JSON.parse(localStorage.getItem('companies')) || [];
            
            if (companies.length === 0) {
                document.getElementById('parsingResult').innerHTML = '<span class="error">No data found</span>';
                return;
            }

            // Test parsing on first 10 companies
            let result = '<div class="success">✓ Address Parsing Test</div>';
            result += '<table><tr><th>Company</th><th>Original Address</th><th>Parsed City</th><th>Parsed State</th><th>Current City</th><th>Current State</th></tr>';

            companies.slice(0, 10).forEach(company => {
                const address = company.originalData?.address || '';
                const parsed = parseAddress(address);
                const cityMatch = parsed.city === company.city;
                const stateMatch = parsed.state === company.state;

                result += `<tr>`;
                result += `<td>${company.title || 'N/A'}</td>`;
                result += `<td>${address}</td>`;
                result += `<td ${!cityMatch ? 'class="highlight"' : ''}>${parsed.city || 'NULL'}</td>`;
                result += `<td ${!stateMatch ? 'class="highlight"' : ''}>${parsed.state || 'NULL'}</td>`;
                result += `<td>${company.city || 'NULL'}</td>`;
                result += `<td>${company.state || 'NULL'}</td>`;
                result += `</tr>`;
            });

            result += '</table>';
            document.getElementById('parsingResult').innerHTML = result;
        }

        function showRawData() {
            const companies = JSON.parse(localStorage.getItem('companies')) || [];
            
            if (companies.length === 0) {
                document.getElementById('rawDataResult').innerHTML = '<span class="error">No data found</span>';
                return;
            }

            // Show first few companies with all their data
            const sample = companies.slice(0, 5);
            let result = '<div class="info">Raw Data Sample (first 5 companies):</div>';
            result += '<pre>' + JSON.stringify(sample, null, 2) + '</pre>';

            document.getElementById('rawDataResult').innerHTML = result;
        }

        function fixMissingCityState() {
            const companies = JSON.parse(localStorage.getItem('companies')) || [];
            
            if (companies.length === 0) {
                document.getElementById('fixResult').innerHTML = '<span class="error">No data found</span>';
                return;
            }

            let updated = 0;
            companies.forEach(company => {
                const address = company.originalData?.address;
                if (address) {
                    const { city, state } = parseAddress(address);
                    if (city !== company.city || state !== company.state) {
                        company.city = city;
                        company.state = state;
                        updated++;
                    }
                }
            });

            // Save back to localStorage
            localStorage.setItem('companies', JSON.stringify(companies));

            let result = `<div class="success">✓ Re-parsing Complete</div>`;
            result += `<div><strong>Companies Updated:</strong> ${updated}</div>`;
            result += `<div>Data has been saved to localStorage. Refresh the main app to see changes.</div>`;

            document.getElementById('fixResult').innerHTML = result;
        }

        // Auto-run analysis on page load
        window.onload = function() {
            analyzeCurrentData();
        }
    </script>
</body>
</html>
