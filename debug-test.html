<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Test - Cold Calling App</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        button { padding: 10px 20px; margin: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Debug Test - Cold Calling App</h1>
    
    <div class="test-section">
        <h2>Test Data Setup</h2>
        <button onclick="setupTestData()">Setup Test Data</button>
        <button onclick="clearTestData()">Clear Test Data</button>
        <div id="dataResult"></div>
    </div>

    <div class="test-section">
        <h2>Filter Test</h2>
        <button onclick="testFilters()">Test Filter Logic</button>
        <div id="filterResult"></div>
    </div>

    <div class="test-section">
        <h2>PowerDialer Test</h2>
        <button onclick="testPowerDialer()">Test PowerDialer Logic</button>
        <div id="dialerResult"></div>
    </div>

    <div class="test-section">
        <h2>Console Logs</h2>
        <button onclick="showLogs()">Show Recent Logs</button>
        <div id="logsResult"></div>
    </div>

    <script>
        // Capture console logs
        const logs = [];
        const originalLog = console.log;
        const originalWarn = console.warn;
        const originalError = console.error;

        console.log = function(...args) {
            logs.push({type: 'log', message: args.join(' '), time: new Date().toLocaleTimeString()});
            originalLog.apply(console, args);
        };

        console.warn = function(...args) {
            logs.push({type: 'warn', message: args.join(' '), time: new Date().toLocaleTimeString()});
            originalWarn.apply(console, args);
        };

        console.error = function(...args) {
            logs.push({type: 'error', message: args.join(' '), time: new Date().toLocaleTimeString()});
            originalError.apply(console, args);
        };

        function setupTestData() {
            const testData = [
                {
                    id: 'test_1',
                    title: 'Charlotte Power Wash',
                    phone: '(*************',
                    website: 'https://charlottepowerwash.com',
                    email: '<EMAIL>',
                    contactName: 'John Smith',
                    city: 'Charlotte',
                    state: 'NC',
                    disposition: null,
                    notes: '',
                    originalData: { address: '123 Main St, Charlotte, NC 28202' }
                },
                {
                    id: 'test_2',
                    title: 'Raleigh Clean Co',
                    phone: '(*************',
                    website: 'https://raleighclean.com',
                    email: '<EMAIL>',
                    contactName: 'Jane Doe',
                    city: 'Raleigh',
                    state: 'NC',
                    disposition: 'not-available',
                    notes: 'Called, no answer',
                    originalData: { address: '456 Oak Ave, Raleigh, NC 27601' }
                },
                {
                    id: 'test_3',
                    title: 'Miami Wash LLC',
                    phone: '(*************',
                    website: 'https://miamiwash.com',
                    email: '<EMAIL>',
                    contactName: 'Carlos Rodriguez',
                    city: 'Miami',
                    state: 'FL',
                    disposition: 'sold',
                    notes: 'Interested in monthly service',
                    originalData: { address: '789 Beach Blvd, Miami, FL 33101' }
                }
            ];

            localStorage.setItem('companies', JSON.stringify(testData));
            document.getElementById('dataResult').innerHTML = '<span class="success">✓ Test data setup complete</span>';
            console.log('Test data setup with', testData.length, 'companies');
        }

        function clearTestData() {
            localStorage.removeItem('companies');
            localStorage.removeItem('dialerPosition');
            document.getElementById('dataResult').innerHTML = '<span class="info">Test data cleared</span>';
            console.log('Test data cleared');
        }

        function testFilters() {
            const companies = JSON.parse(localStorage.getItem('companies')) || [];
            
            if (companies.length === 0) {
                document.getElementById('filterResult').innerHTML = '<span class="error">No test data found. Setup test data first.</span>';
                return;
            }

            // Test city filtering
            const charlotteCompanies = companies.filter(c => c.city === 'Charlotte');
            const ncCompanies = companies.filter(c => c.state === 'NC');
            const soldCompanies = companies.filter(c => c.disposition === 'sold');

            let result = '<div class="success">✓ Filter tests:</div>';
            result += `<div>Total companies: ${companies.length}</div>`;
            result += `<div>Charlotte companies: ${charlotteCompanies.length}</div>`;
            result += `<div>NC companies: ${ncCompanies.length}</div>`;
            result += `<div>Sold companies: ${soldCompanies.length}</div>`;

            document.getElementById('filterResult').innerHTML = result;
            console.log('Filter test completed');
        }

        function testPowerDialer() {
            const companies = JSON.parse(localStorage.getItem('companies')) || [];
            
            if (companies.length === 0) {
                document.getElementById('dialerResult').innerHTML = '<span class="error">No test data found. Setup test data first.</span>';
                return;
            }

            // Simulate PowerDialer initialization
            const startIndex = 0;
            const currentCompany = companies[startIndex];

            let result = '<div class="success">✓ PowerDialer test:</div>';
            result += `<div>Companies available: ${companies.length}</div>`;
            result += `<div>Starting index: ${startIndex}</div>`;
            result += `<div>Current company: ${currentCompany.title}</div>`;
            result += `<div>Company city: ${currentCompany.city}</div>`;
            result += `<div>Company state: ${currentCompany.state}</div>`;

            document.getElementById('dialerResult').innerHTML = result;
            console.log('PowerDialer test completed');
        }

        function showLogs() {
            let result = '<div class="info">Recent console logs:</div>';
            result += '<pre>';
            
            logs.slice(-10).forEach(log => {
                const className = log.type === 'error' ? 'error' : log.type === 'warn' ? 'error' : 'info';
                result += `[${log.time}] ${log.type.toUpperCase()}: ${log.message}\n`;
            });
            
            result += '</pre>';
            document.getElementById('logsResult').innerHTML = result;
        }

        // Auto-setup on page load
        window.onload = function() {
            console.log('Debug test page loaded');
        }
    </script>
</body>
</html>
