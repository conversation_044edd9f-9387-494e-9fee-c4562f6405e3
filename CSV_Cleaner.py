import pandas as pd
import os
import time
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

LEAD_DUMP_DIR = os.path.join(os.path.dirname(__file__), 'Lead_Dump')
CLEAN_LEADS_DIR = os.path.join(os.path.dirname(__file__), 'Clean_Leads')

# Define which columns you want to keep (case insensitive matching)
KEEP_PATTERNS = [
    r"name", r"phone", r"address", r"website", r"social", r"facebook",
    r"instagram", r"linkedin", r"twitter", r"email", r"title" 
]

# Optionally: rename columns to standardized names
RENAME_MAP = {
    # e.g. original column names to your desired target names
    "title": "Name",
    "phoneUnformatted": "Phone",
    # add more if needed
}

def clean_csv(input_csv, output_csv):
    df = pd.read_csv(input_csv, dtype=str)
    cols_to_keep = []
    for col in df.columns:
        lower = col.lower()
        for pat in KEEP_PATTERNS:
            if pat in lower:
                cols_to_keep.append(col)
                break
    df_clean = df[cols_to_keep].rename(columns=RENAME_MAP)
    df_clean.to_csv(output_csv, index=False)
    print(f"Cleaned: {input_csv} -> {output_csv}")
    print(f"Kept columns: {list(df_clean.columns)}")

class CSVHandler(FileSystemEventHandler):
    def on_created(self, event):
        if not event.is_directory and event.src_path.lower().endswith('.csv'):
            filename = os.path.basename(event.src_path)
            output_path = os.path.join(CLEAN_LEADS_DIR, filename)
            try:
                clean_csv(event.src_path, output_path)
            except Exception as e:
                print(f"Failed to clean {event.src_path}: {e}")

def main():
    if not os.path.exists(CLEAN_LEADS_DIR):
        os.makedirs(CLEAN_LEADS_DIR)
    event_handler = CSVHandler()
    observer = Observer()
    observer.schedule(event_handler, LEAD_DUMP_DIR, recursive=False)
    observer.start()
    print(f"Watching {LEAD_DUMP_DIR} for new CSV files...")
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        observer.stop()
    observer.join()

if __name__ == "__main__":
    main()